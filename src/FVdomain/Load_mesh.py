"""Utility functions for reading the datasets."""

import sys
import os
file_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.append(file_dir)

import matplotlib
matplotlib.use("agg")

import json
import random
import torch
from torch.utils.data import Dataset
import numpy as np
import h5py
import math
from Utils import get_param
from Utils.utilities import NodeType
from FVdomain.Set_BC import velocity_profile
from FVmodel.FVdiscretization.FVgrad import compute_normal_matrix

class CFDdatasetBase:
    # Base class for CFDdataset with process_trajectory method
    @staticmethod
    def select_PDE_coef(theta_PDE_list=None):
        (
            mean_U,
            rho,
            mu,
            source,
            aoa,
            dt,
            L,
        ) = random.choice(theta_PDE_list)

        return (
            mean_U, 
            rho, 
            mu, 
            source,
            aoa, 
            dt, 
            L,
        )

    @staticmethod
    def init_env(
        mesh,
        mean_u=None,
    ):
        # init uvwp field
        cpd_centroid = mesh["cpd|centroid"].to(torch.float32)
        uvw_cell, p_cell = velocity_profile(
            inlet_node_pos=cpd_centroid,
            mean_u=mean_u,
            aoa=mesh["aoa"],
            inlet_type=mesh["init_field_type"],
        )
        
        # set uniform initial field value
        uvwp_cell = torch.cat(
            (
                uvw_cell, 
                p_cell
            ),
            dim=1
        ).to(torch.float32)
        
        # generate BC mask
        cell_type = mesh["cpd|cell_type"].long().squeeze()
        Wall_mask = (cell_type== NodeType.WALL_BOUNDARY)
        Inlet_mask = (cell_type== NodeType.INFLOW)
        Dirichlet_mask = Inlet_mask | Wall_mask

        # generate velocity profile
        inlet_uvwp_face, _ = velocity_profile(
            inlet_node_pos=cpd_centroid[Inlet_mask],
            mean_u=mean_u,
            aoa=mesh["aoa"],
            inlet_type=mesh["inlet_type"],
        )
        inlet_uvwp_face = inlet_uvwp_face.to(torch.float32)
        
        # apply velocity profile and boundary condition
        uvwp_cell[Inlet_mask,0:3] = inlet_uvwp_face[:,0:3]
        uvwp_cell[Wall_mask,0:3] = 0

        # store target node for dirchlet BC and make dimless if possible
        mesh["target|uvwp"] = uvwp_cell[Dirichlet_mask,0:3].clone() / mean_u
        
        return mesh, uvwp_cell

    @staticmethod
    def set_theta_PDE(mesh, params, mean_velocity, rho, mu, source, aoa, dt, dL):
        """
        设置用于 PDE 求解的参数 theta_PDE, 并将其添加到 mesh 字典中。

        参数：
        - mesh: 包含网格信息的字典。
        - params: 参数配置对象。
        - mean_velocity: 入口平均速度的标量值。
        - rho: 流体密度。
        - mu: 流体黏度。
        - source: 源项大小。
        - aoa: 攻角(angle of attack)以度为单位。
        - dt: 时间步长。
        - dL: 特征长度。

        返回：
        - mesh: 更新后的网格信息字典，包含了计算所得的 PDE 参数。
        - U_in: 乘上攻角之后的入口速度的二维张量。
        """
        U_in = mean_velocity*torch.tensor(
            [math.cos(math.radians(aoa)), math.sin(math.radians(aoa))]
        )
        
        mesh_pos = mesh["node|pos"][0]
        
        theta_PDE = mesh["theta_PDE_bak"]
        
        unsteady_coeff = theta_PDE["unsteady"]

        continuity_eq_coeff = theta_PDE["continuity"]

        convection_coeff = theta_PDE["convection"]

        grad_p_coeff = theta_PDE["grad_p"] / rho

        diffusion_coeff = (
            (mu / mean_velocity) if 0 == convection_coeff else # convection_coeff=0 means poisson equation
            (mu / (rho * mean_velocity)) # Navier-Stokes equation
        )

        source_term = source / mean_velocity # if params.dimless else source

        dt_cell = dt * mean_velocity # if params.dimless else dt
        
        theta_PDE = torch.tensor(
            [
                unsteady_coeff, # 1
                continuity_eq_coeff, # 2
                convection_coeff, # 3
                grad_p_coeff, # 4
                diffusion_coeff, # 5
                source_term, # 6
                U_in[0].item(), # 7
                U_in[1].item(), # 8
                mesh["Re"], # 9
            ],
            device=mesh_pos.device,
            dtype=torch.float32,
        ).view(1,-1)
        mesh["theta_PDE"] = theta_PDE
        
        mesh["dt_graph"] = torch.tensor(
            [dt_cell],
            device=mesh_pos.device,
            dtype=torch.float32,
        ).view(1,-1)
        
        mesh["sigma"] = torch.from_numpy(np.array(mesh["sigma"])).view(1,-1)
        
        mesh["uvwp_dim"] = torch.tensor(
            [[mean_velocity, mean_velocity, mean_velocity, (mean_velocity**2)]],
            device=mesh_pos.device,
            dtype=torch.float32,
        ).view(1,-1)

        return mesh, U_in

    @staticmethod
    def makedimless(
        mesh, params, case_name=None, theta_PDE_list=None
    ):
        (
            mean_u,
            rho,
            mu,
            source,
            aoa,
            dt,
            L,
        ) = CFDdatasetBase.select_PDE_coef(theta_PDE_list)
        
        mesh["mean_u"] = torch.tensor(mean_u, dtype=torch.float32)
        mesh["rho"] = torch.tensor(rho, dtype=torch.float32)
        mesh["mu"] = torch.tensor(mu, dtype=torch.float32)
        mesh["source"] = torch.tensor(source, dtype=torch.float32)
        mesh["aoa"] = torch.tensor(aoa, dtype=torch.float32)
        mesh["dt"] = torch.tensor(dt, dtype=torch.float32)
        mesh["L"] = torch.tensor(L, dtype=torch.float32)
        mesh["Re"] = torch.tensor(rho * mean_u * L, dtype=torch.float32) / \
            mu if mu!=0 else torch.tensor(0, dtype=torch.float32)
        
        (
            mesh,
            U_inlet,
        ) = CFDdatasetBase.set_theta_PDE(
            mesh, params, mean_u, rho, mu, source, aoa, dt, L
        )

        return mesh, mean_u, U_inlet

    @staticmethod
    def calc_WLSQ_A_B_normal_matrix(mesh, order):

        if not "A_cell_to_cell" in mesh.keys():
            
            """>>> compute WLSQ cell to cell left A matrix >>>"""
            cpd_centroid = mesh["cpd|centroid"]
            cpd_neighbor_cell_x = mesh["cpd|neighbor_cell_x"].long()

            """ >>> compute WLSQ cell to cell left A matrix >>> """
            (A_cell_to_cell, two_way_B_cell_to_cell) = compute_normal_matrix(
                order=order,
                mesh_pos=cpd_centroid,
                edge_index=cpd_neighbor_cell_x, # 默认应该是仅包含1阶邻居点+构成共点的单元的所有点
            )
            
            mesh["A_cell_to_cell"] = A_cell_to_cell.to(torch.float32)
            mesh["single_B_cell_to_cell"] = (
                torch.chunk(two_way_B_cell_to_cell, 2, dim=0)[0]
            ).to(torch.float32)
            """ <<< compute WLSQ cell to cell right B matrix<<< """

        return mesh
    
    @staticmethod
    def transform_mesh(
        mesh, 
        params=None
    ):
        
        theta_PDE_list = mesh["theta_PDE_list"]
        case_name = mesh["case_name"]
        
        mesh, mean_u, U_inlet = CFDdatasetBase.makedimless(
            mesh,
            theta_PDE_list=theta_PDE_list,
            case_name=case_name,
            params=params,
        )

        mesh = CFDdatasetBase.calc_WLSQ_A_B_normal_matrix(mesh,params.order)
        
        mesh, init_uvwp_cell = CFDdatasetBase.init_env(
            mesh,        
            mean_u=mean_u,
        )

        return mesh, init_uvwp_cell

class H5CFDdataset(Dataset):
    def __init__(self, params, file_list):
        super().__init__()

        self.file_list = file_list
        self.params = params
        
    def __getitem__(self, index):
        path = self.file_list[index]
        file_dir = os.path.dirname(path)
        h5_file = h5py.File(path, "r")

        try:
            BC_file = json.load(open(f"{file_dir}/BC.json", "r"))
        except:
            raise ValueError(f"BC.json is not found in the {path}")

        mesh = {"case_name":h5_file.attrs['case_name']}
        
        # convert to tensors
        for key in h5_file.keys():
            value = h5_file[key][()]
            if isinstance(value, np.ndarray):
                # convert numpy array to torch tensor
                mesh[key] = torch.from_numpy(value)
            else:
                mesh[key] = value

        # import all BC.json item into mesh dict
        for key, value in BC_file.items():
            mesh[key] = value
        mesh["theta_PDE_bak"] = mesh["theta_PDE"] # 后续生成单独case参数时候theta_PDE会被覆盖，所以备份一下
        
        # generate all valid theta_PDE combinations
        theta_PDE = mesh["theta_PDE_bak"]
        theta_PDE_list = (
                    get_param.generate_combinations(
                        U_range=theta_PDE["inlet"],
                        rho_range=theta_PDE["rho"],
                        mu_range=theta_PDE["mu"],
                        source_range=theta_PDE["source"],
                        aoa_range=theta_PDE["aoa"],
                        dt=theta_PDE["dt"],
                        L=theta_PDE["L"],
                        Re_max=theta_PDE["Re_max"],
                        Re_min=theta_PDE["Re_min"],
                    )
                )
        mesh["theta_PDE_list"] = theta_PDE_list
        
        # start to calculate other attributes like stencil, WLSQ matrix, etc.
        mesh_transformed, init_uvwp_cell = CFDdatasetBase.transform_mesh(
            mesh, 
            self.params
        )

        # return to CPU!
        return mesh_transformed, init_uvwp_cell 

    def __len__(self):
        return len(self.file_list)

