"""
HeteroData-based dataset for FV domain.
Defines the heterogeneous graph data structure and dataset.
"""

import sys
import os
file_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.append(file_dir)

import torch
from torch_geometric.data import InMemoryDataset, HeteroData

class HeteroGraphBase(HeteroData):
    """
    Custom heterogeneous graph data structure with precise batching control.

    Node types:
    - 'node': Physical mesh nodes/vertices
    - 'face': Mesh faces/edges
    - 'cell': Mesh cells
    - 'cpd_cell': Compound cells (for WLSQ)
    - 'graph': Graph-level attributes
    """

    _skip_to_keys = {''}

    def __inc__(self, key, value, *args, **kwargs):
        """
        Specifies how to increment attributes when concatenating graphs in a batch.
        This follows the exact same logic as the original GraphBase implementation.
        """
        # Helper function to safely get attributes with default values
        def safe_get_attr(attr_name, default=0):
            return getattr(self, attr_name, default)

        offset_rules = {
            "edge_index": self.num_nodes ,
            "face": self.num_nodes , # keep pyg default value
            
            "face_node": safe_get_attr('_num_nodes'),
            
            "cells_node": safe_get_attr('_num_nodes'),
            "cells_node_ptr": safe_get_attr('_num_cells'),

            "cyclic_face": safe_get_attr('_num_faces'),
            "face_node_ptr": safe_get_attr('_num_faces'),

            "cells_face": safe_get_attr('_num_faces'),
            "cells_face_ptr": safe_get_attr('_num_cells'),
            
            "owner": safe_get_attr('_num_cells'),
            "neighbor_cell": safe_get_attr('_num_cpd_cells'),
            "cpd_neighbor_cell": safe_get_attr('_num_cpd_cells'),
            "cpd_neighbor_cell_x": safe_get_attr('_num_cpd_cells'),
            "cpd_neighbor_cell_non_cyclic": safe_get_attr('_num_cpd_cells_cyclic'),

            # No offset for these attributes
            "case_name": 0,
            "query": 0,
            "grids": 0,
            "pos": 0,
            "A_cell_to_cell": 0,
            "A_cell_to_cell_x": 0,
            "B_cell_to_cell": 0,
            "B_cell_to_cell_x": 0,
            "single_B_cell_to_cell": 0,
            "extra_B_cell_to_cell": 0,
            "cells_volume": 0,
            "node_type": 0,
            "graph_index": 0,
            "theta_PDE": 0,
            "sigma": 0,
            "uvwp_dim": 0,
            "dt_graph": 0,
            "x": 0,
            "y": 0,
            "m_ids": 0,
            "m_gs": 0,
            "global_idx": 0,
            "cell_type": 0,
            "cpd_centroid": 0,
            "face_area": 0,
            "face_type": 0,
            "cells_face_unv": 0,
            "_num_nodes": 0,
            "_num_faces": 0,
            "_num_cells": 0,
            "_num_cpd_cells": 0,
        }

        # Extract attribute name from HeteroData key format
        if isinstance(key, tuple) and len(key) >= 2:
            attr_name = key[-1]  # Last element is the attribute name
        else:
            attr_name = key

        return offset_rules.get(attr_name, super().__inc__(key, value, *args, **kwargs))

    def __cat_dim__(self, key, value, *args, **kwargs):
        """
        Specifies the dimension along which attributes should be concatenated when creating a batch.
        This follows the exact same logic as the original GraphBase implementation.
        """
        # Extract attribute name from the key
        if isinstance(key, tuple) and len(key) >= 2:
            if len(key) == 4:
                node_type1, edge_type, node_type2, attr_name = key
            elif len(key) == 2:
                node_type, attr_name = key
            else:
                attr_name = key[-1]
        else:
            attr_name = key

        # Define concatenation dimension rules (same as original)
        cat_dim_rules = {
            "x": 0,  # Node features: [N, F_node]
            "pos": 0,  # Node positions: [N, D_pos]
            "y": 0,  # Target values: [N_target, F_target] or [F_target]
            "edge_index": 1,  # Edge indices: [2, num_edges], concatenate along dim 1
            "face_node": 0,  # Face-node connectivity: [num_faces*nodes_per_face] - CORRECTED!
            "face_node_ptr": 0,
            "cyclic_face": 1,
            "face": 0,  # Cell-node connectivity
            "owner": 0, # [num_faces]
            "neighbor_cell": 1,  # [2, num_neighbor_cell_edges]
            "cpd_neighbor_cell_non_cyclic": 1, #[2, num_neighbor_cell_non_cyclic_edges]
            "cpd_neighbor_cell_x": 1,  # [2, num_neighbor_cell_x_edges]
            "cpd_neighbor_cell": 1,  # [2, num_cpd_neighbor_edges] - edge_index format
            "graph_index": 0,  # [batch_size] or [1]
            "global_idx": 0,  # [N]
            "cell_type": 0,
            "cpd_centroid": 0,
            "cells_index": 0,
            "cells_volume": 0,
            "face_area": 0,
            "face_type": 0,
            "cells_face_unv": 0,
            "cells_node": 0,
            "cells_node_ptr": 0,
            "cells_face": 0,
            "cells_face_ptr": 0,
            "A_cell_to_cell": 0,
            "single_B_cell_to_cell": 0,
            "_num_nodes": 0,
            "_num_faces": 0,
            "_num_cells": 0,
            "_num_cpd_cells": 0,
        }

        return cat_dim_rules.get(attr_name, super().__cat_dim__(key, value, *args, **kwargs))

    def to(self, *args, exclude_keys=None, **kwargs):
        """Move tensors to target device/dtype, excluding specified keys."""
        if exclude_keys is None:
            exclude_keys = []
        elif isinstance(exclude_keys, str):
            exclude_keys = [exclude_keys]

        all_exclude_keys = set(exclude_keys) | self._skip_to_keys

        for store in self.stores:
            for key, item in store.items():
                if isinstance(item, torch.Tensor) and key not in all_exclude_keys:
                    store[key] = item.to(*args, **kwargs)

        return self

    def cuda(self, device=None, exclude_keys=None):
        """Move to CUDA device with optional key exclusion."""
        return self.to(device or 'cuda', exclude_keys=exclude_keys)


class HeteroGraphDataset(InMemoryDataset):
    """
    Dataset that creates HeteroData objects from the base dataset.
    """

    def __init__(self, base_dataset):
        super().__init__()
        self.base_dataset = base_dataset

    @property
    def pool(self):
        return self.base_dataset.meta_pool

    def len(self):
        return len(self.pool)

    def __getitem__(self, idx):
        """Support direct indexing: dataset[idx] or dataset[[1,2,3]]"""
        if isinstance(idx, (list, tuple)):
            # Batch indexing: return list of graphs
            return [self.get(i) for i in idx]
        else:
            # Single indexing: return single graph
            return self.get(idx)

    def get(self, idx):
        """
        Creates a heterogeneous graph from the base dataset.
        """
        minibatch_data = self.pool[idx]
        hetero_graph = HeteroGraphBase()
        
        # === NODE DATA ===
        mesh_pos = minibatch_data["node|pos"].to(torch.float32)
        hetero_graph['node'].pos = mesh_pos
        hetero_graph['node'].num_nodes = mesh_pos.shape[0]
        
        # === FACE DATA ===
        face_area = minibatch_data["face|face_area"].to(torch.float32)
        face_type = minibatch_data["face|face_type"].long().squeeze()
        face_pos = minibatch_data["face|face_pos"].to(torch.float32)

        hetero_graph['face'].pos = face_pos
        hetero_graph['face'].face_area = face_area
        hetero_graph['face'].face_type = face_type
        hetero_graph['face'].num_nodes = face_pos.shape[0]

        # === CELL DATA ===
        cells_volume = minibatch_data["cell|cells_volume"].to(torch.float32)
        hetero_graph['cell'].cells_volume = cells_volume
        hetero_graph['cell'].num_nodes = cells_volume.shape[0]
        
        # === CPD CELL DATA ===
        cpd_centroid = minibatch_data["cpd|centroid"].to(torch.float32)
        cell_type = minibatch_data["cpd|cell_type"].long()
        global_idx = minibatch_data["global_idx"].long()
        uvwp_cell = self.base_dataset.uvwp_cell_pool[global_idx]
        target_on_cell = minibatch_data["target|uvwp"].to(torch.float32)
        
        hetero_graph['cpd_cell'].pos = cpd_centroid
        hetero_graph['cpd_cell'].cell_type = cell_type
        hetero_graph['cpd_cell'].x = uvwp_cell
        hetero_graph['cpd_cell'].y = target_on_cell
        hetero_graph['cpd_cell'].global_idx = global_idx
        hetero_graph['cpd_cell'].num_nodes = cpd_centroid.shape[0]
        
        # === GRAPH-LEVEL DATA ===
        case_name = minibatch_data["case_name"]
        theta_PDE = minibatch_data["theta_PDE"].to(torch.float32)
        uvwp_dim = minibatch_data["uvwp_dim"].to(torch.float32)
        dt_graph = minibatch_data["dt_graph"].to(torch.float32)
        
        hetero_graph['graph'].theta_PDE = theta_PDE
        hetero_graph['graph'].uvwp_dim = uvwp_dim
        hetero_graph['graph'].dt_graph = dt_graph
        hetero_graph['graph'].case_name = torch.tensor([ord(char) for char in case_name], dtype=torch.long)
        hetero_graph['graph'].graph_index = torch.tensor([idx], dtype=torch.long)
        hetero_graph['graph'].num_nodes = 1
        
        # === CONNECTIVITY DATA ===
        # Face-Node connectivity
        face_node = minibatch_data["face_node"].long()
        hetero_graph['face', 'contains', 'node'].face_node = face_node
        
        # Cell-Node connectivity
        cells_node = minibatch_data["cells_node"].long()
        cells_node_ptr = minibatch_data["cells_node_ptr"].long()
        hetero_graph['cell', 'contains', 'node'].cells_node = cells_node
        hetero_graph['cell', 'contains', 'node'].cells_node_ptr = cells_node_ptr
        
        # Cell-Face connectivity
        cells_face = minibatch_data["cells_face"].long()
        cells_face_ptr = minibatch_data["cells_face_ptr"].long()
        face_node_ptr = minibatch_data["face_node_ptr"].long()
        hetero_graph['cell', 'bounded_by', 'face'].cells_face = cells_face
        hetero_graph['cell', 'bounded_by', 'face'].cells_face_ptr = cells_face_ptr
        hetero_graph['face', 'bounds', 'cell'].face_node_ptr = face_node_ptr
        
        # Cell-Cell connectivity
        cpd_neighbor_cell = minibatch_data["cpd|neighbor_cell"].long()
        hetero_graph['cpd_cell', 'neighbors', 'cpd_cell'].cpd_neighbor_cell = cpd_neighbor_cell # 用来做面插值，
        
        # Cyclic face connectivity
        cyclic_face = minibatch_data["cyclic_face"].long()
        cpd_neighbor_cell_non_cyclic = minibatch_data["cpd|neighbor_cell_non_cyclic"].long()
        hetero_graph['face'].cyclic_face = cyclic_face
        hetero_graph['cpd_cell', 'neighbors', 'cpd_cell'].cpd_neighbor_cell_non_cyclic = cpd_neighbor_cell_non_cyclic

        # WLSQ connectivity and matrices
        cpd_neighbor_cell_x = minibatch_data["cpd|neighbor_cell_x"].long()
        A_cell_to_cell = minibatch_data["A_cell_to_cell"].to(torch.float32)
        single_B_cell_to_cell = minibatch_data["single_B_cell_to_cell"].to(torch.float32)
        
        hetero_graph['cpd_cell', 'wlsq_neighbors', 'cpd_cell'].cpd_neighbor_cell_x = cpd_neighbor_cell_x
        hetero_graph['cpd_cell'].A_cell_to_cell = A_cell_to_cell
        hetero_graph['cpd_cell'].single_B_cell_to_cell = single_B_cell_to_cell
        
        # Additional geometric data
        cells_face_unv = minibatch_data['cells_face_normal'].to(torch.float32)
        hetero_graph['cell'].cells_face_unv = cells_face_unv

        # Set the count attributes for proper batching offset calculation
        hetero_graph._num_nodes = mesh_pos.shape[0]
        hetero_graph._num_faces = face_pos.shape[0]
        hetero_graph._num_cells = cells_volume.shape[0]
        hetero_graph._num_cpd_cells = cpd_centroid.shape[0]

        return hetero_graph

