import torch
import numpy as np
import torch.nn as nn
from torch_scatter import scatter_mean
from Utils.utilities import NodeType
from Utils.normalization import Normalizer
from FVmodel.FVdiscretization.FVscheme import Intergrator


class NNmodel(nn.Module):
    def __init__(self, params) -> None:
        super().__init__()

        self.params = params
        if self.params.net =='FVGN':
            from FVmodel.NNmodels.FVGN.FVGN import Simulator
        elif self.params.net =='TransFVGN_v2' or self.params.net =='TransFVGN':
            from FVmodel.NNmodels.TransFVGN.TransFVGN_v2 import Simulator
        else:
            raise ValueError(f"Unknown net type: {self.params.net}. Please check your params.net setting.")
        
        self.simulator = Simulator(
            message_passing_num=params.message_passing_num,
            node_input_size=params.node_input_size,
            edge_input_size=params.node_input_size + 4,  # 4 for relative edge attr(pos+norm(pos)=3+1)
            node_output_size=params.node_output_size,
            drop_out=False,
            hidden_size=params.hidden_size,
            params=params,
        )
        
        self.node_norm = Normalizer(size=params.node_input_size - params.node_phi_size,
                                    max_accumulations=params.dataset_size)
        
        self.integrator = Intergrator(params=params)

        self.initialize_weights()

    def initialize_weights(self):
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            nn.init.trunc_normal_(m.weight, std=0.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, (nn.LayerNorm, nn.BatchNorm1d)):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
            
    def cal_relative_edge_attr(self, graph):

        # No more permuting edge direction
        senders, receivers = graph.cpd_neighbor_cell

        releative_x_attr = (
            graph.x[senders]
            - graph.x[receivers]
        )
        releative_x_pos = (
            graph.cpd_centroid[senders]
            - graph.cpd_centroid[receivers]
        )
        releative_x_attr = torch.cat(
            (
                releative_x_attr,
                releative_x_pos,
                torch.norm(releative_x_pos, p=2, dim=-1, keepdim=True),
            ),
            dim=-1,
        )

        graph.edge_attr = releative_x_attr

        return graph
    
    def normalize_graph_features(self, x, batch):
        # 检查输入张量和 batch 索引的形状
        assert x.dim() == 2, "Input tensor x should be 2-dimensional"
        assert batch.dim() == 1, "Batch tensor should be 1-dimensional"
        assert x.size(0) == batch.size(0), "The first dimension of x and batch should be the same"

        mean = scatter_mean(x, batch, dim=0)
        residual = x - mean[batch]
        var = scatter_mean(residual**2, batch, dim=0)
        std = torch.sqrt(var)
        x = residual / (std[batch] + 1e-8)
        # x = residual
        
        return x
    
    def update_x_attr(
        self,
        graph_cell,
        graph_Index,
    ):

        if graph_cell.norm_uvwp: # This param was set in src/FVdomain/Graph_loader.py --> datapreprocessing()
            graph_cell.x[:,:self.params.node_phi_size] = self.normalize_graph_features(graph_cell.x[:,:self.params.node_phi_size], graph_cell.batch)
            graph_cell.norm_uvwp=False
        else:
            raise ValueError(" src/FVmodel/importer.py The graph node features have already been normalized, please check the graph.norm_uvwp")
        
        if graph_cell.norm_global: # This param was set in src/FVdomain/Graph_loader.py --> datapreprocessing()
            graph_cell.x[:,self.params.node_phi_size:] = self.node_norm(graph_cell.x[:,self.params.node_phi_size:])
            graph_cell.norm_global=False
        
        return graph_cell

    def update_edge_attr(
        self,
        graph,
    ):
        # 计算relative u,v,p, pos(2维), norm(pos)， 一共6维
        graph = self.cal_relative_edge_attr(graph)

        return graph

    def forward(
        self,
        graph_node,
        graph_face,
        graph_cell,
        graph_cell_x,
        graph_Index,
        is_training=True,
    ):

        if is_training:
            
            # Register geometric quantities for interpolation operations
            self.integrator.register_geometrics(
                graph_node, graph_face, graph_cell, graph_Index, force_register=True
            )
            
            # 取出上一时刻uvw来作时间差分
            uvw_old_cpd_cell = (
                graph_cell.x[:, 0:3] / graph_Index.uvwp_dim[graph_cell.batch, 0:3]
            )

            # update normalized value
            graph_cell = self.update_x_attr(
                graph_cell=graph_cell,
                graph_Index=graph_Index,
            )

            graph_cell = self.update_edge_attr(
                graph=graph_cell,
            )  # 因为每次都会重复计算relative edge attr, 所以每次都必定norm edge attr

            uvwp_new_cpd_cell_non_cyclic = self.simulator(
                graph_cell.x[self.integrator.mask_cpd_cell_non_cyclic],
                graph_cell.edge_attr,
                edge_index=graph_cell.cpd_neighbor_cell_non_cyclic,
                batch=graph_cell.batch[self.integrator.mask_cpd_cell_non_cyclic],
            )
            
            # Enforce Dirichlet boundary conditions on velocity components only
            uvwp_new_cpd_cell = self.integrator.enforce_boundary_condition(uvwp_new_cpd_cell_non_cyclic, graph_cell)
            
            # Intergrate all flux at every edge`s of all cells
            (
                loss_cont,
                loss_momentum,
                uvwp_new_cpd_cell,
            ) = self.integrator(
                uvw_old_cpd_cell=uvw_old_cpd_cell,
                uvwp_new_cpd_cell=uvwp_new_cpd_cell,
                graph_node=graph_node,
                graph_face=graph_face,
                graph_cell=graph_cell,
                graph_cell_x=graph_cell_x,
                graph_Index=graph_Index,
                params=self.params,
            )

            # reverse dimless for storing
            uvwp_new_with_dim_cpd_cell = uvwp_new_cpd_cell*graph_Index.uvwp_dim[graph_cell.batch]

            return (
                loss_cont,
                loss_momentum,
                uvwp_new_with_dim_cpd_cell,
            )
        else:
            
            # Register geometric quantities for interpolation operations
            self.integrator.register_geometrics(
                graph_node, graph_face, graph_cell, graph_Index, force_register=False
            )
            
            # update normalized value
            graph_cell = self.update_x_attr(
                graph_cell=graph_cell,
                graph_Index=graph_Index,
            )

            graph_cell = self.update_edge_attr(
                graph=graph_cell,
            )  # 因为每次都会重复计算relative edge attr, 所以每次都必定norm edge attr

            uvwp_new_cpd_cell_non_cyclic = self.simulator(
                graph_cell.x[self.integrator.mask_cpd_cell_non_cyclic],
                graph_cell.edge_attr,
                edge_index=graph_cell.cpd_neighbor_cell_non_cyclic,
                batch=graph_cell.batch[self.integrator.mask_cpd_cell_non_cyclic],
            )
            
            # Enforce Dirichlet boundary conditions on velocity components only
            uvwp_new_cpd_cell = self.integrator.enforce_boundary_condition(uvwp_new_cpd_cell_non_cyclic, graph_cell)
            
            # reverse dimless for storing
            uvwp_new_with_dim_cpd_cell = uvwp_new_cpd_cell*graph_Index.uvwp_dim[graph_cell.batch]
            
            return uvwp_new_with_dim_cpd_cell

    def load_checkpoint(self, optimizer=None, scheduler=None, ckpdir=None, device=None):
        if ckpdir is None:
            ckpdir = self.model_dir
        dicts = torch.load(ckpdir, map_location=device)
        self.load_state_dict(dicts["model"])
        keys = list(dicts.keys())
        keys.remove("model")

        if optimizer is not None and "optimizer0" in dicts:
            if not isinstance(optimizer, list):
                optimizer = [optimizer]
            for i, o in enumerate(optimizer):
                if f"optimizer{i}" in dicts:
                    o.load_state_dict(dicts[f"optimizer{i}"])
                    keys.remove(f"optimizer{i}")

        if scheduler is not None and "scheduler0" in dicts:
            if not isinstance(scheduler, list):
                scheduler = [scheduler]
            for i, s in enumerate(scheduler):
                if f"scheduler{i}" in dicts:
                    s.load_state_dict(dicts[f"scheduler{i}"])
                    keys.remove(f"scheduler{i}")

        if not self.training:
            keys = [
                key
                for key in keys
                if not (key.startswith("optimizer") or key.startswith("scheduler"))
            ]

        print(f"Simulator model and optimizer/scheduler loaded checkpoint {ckpdir}")

    def save_checkpoint(self, path=None, optimizer=None, scheduler=None):

        if path is None:
            path = self.model_dir

        to_save = {"model": self.state_dict()}

        if optimizer is not None:
            if not isinstance(optimizer, list):
                optimizer = [optimizer]
            for i, o in enumerate(optimizer):
                to_save[f"optimizer{i}"] = o.state_dict()

        if scheduler is not None:
            if not isinstance(scheduler, list):
                scheduler = [scheduler]
            for i, s in enumerate(scheduler):
                to_save[f"scheduler{i}"] = s.state_dict()

        torch.save(to_save, path)

        print(f"Simulator model saved at {path}")
