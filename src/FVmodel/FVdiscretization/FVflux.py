import sys
import os
file_dir = os.path.dirname(__file__)
sys.path.append(file_dir)

import torch
from Utils.utilities import NodeType
from FVmodel.FVdiscretization.FVInterpolation import FV_Interpolation

class FV_flux(FV_Interpolation):
    def __init__(self, conserved=True):
        """
        Finite Volume flux calculation class for computing convective, diffusive, and pressure fluxes.
        
        Inherits from Interplot to provide interpolation functionality for flux calculations.
        Used as a base class for flux-related operations in finite volume discretization.
        
        Args:
            conserved (bool): Flag to indicate if conserved form of equations is used.
                            Default is True.
        """
        super(FV_flux, self).__init__()
        
        self.conserved = conserved

    def convective_flux(
            self, 
            uvw_face=None, 
            phi_hat_cpd_cell = None, 
            grad_phi_hat_cpd_cell = None, 
            grad_phi_hat_face = None, 
            scheme="LinearUpwind"
        ):
        """ 
        Compute convective flux for momentum equations.
        
        Calculates the convective flux using velocity values at face centers,
        weighted by convection coefficients. The convection scheme can be
        specified to control the interpolation of velocity across faces.
        
        Args:
            uvw_face (torch.Tensor): Velocity values at face centers [N_faces, 3].
            phi_hat_cpd_cell (torch.Tensor): High-order interpolated values at cell centers [N_cpd_cells, N_variables].
            grad_phi_hat_cpd_cell (torch.Tensor): High-order gradients at cell centers [N_cpd_cells, N_variables, 3].
            grad_phi_hat_face (torch.Tensor): High-order gradients at face centers [N_faces, N_variables, 3].
            scheme (str): Interpolation scheme to use. Options: "Linear", "Linear corrected", "Upwind", "LinearUpwind", "limitedLinearV"
        
        Returns:
            torch.Tensor: Convective flux tensor [N_cells_faces, 3, 3].
                         Represents the convective momentum flux through each cell face.    
        """

        U_face = self.div_phic_to_faces(
            uvw_face=uvw_face, # 用于迎风方向判断
            phi_cpd_cell=phi_hat_cpd_cell,
            grad_phi_cpd_cell=grad_phi_hat_cpd_cell,
            grad_phi_face=grad_phi_hat_face,
            scheme=scheme,
        )
        UU_face = torch.matmul(
            uvw_face[:,:,None], U_face[:,None,:]
        )
        convection_flux_face = UU_face * (self.convection_coeff.unsqueeze(1))

        return convection_flux_face
        
    def diffusion_flux(self, grad_uvw_hat_face):
        """
        Compute viscous (diffusion) flux for momentum equations.
        
        Calculates the diffusive flux using velocity gradients at face centers,
        weighted by diffusion (viscosity) coefficients.
        
        Args:
            grad_uvw_face_hat (torch.Tensor): Velocity gradients at face centers 
                                            [N_faces, 3, 3]. Shape is [face, velocity_component, spatial_derivative].
        
        Returns:
            torch.Tensor: Viscous flux tensor [N_cells_faces, 3, 3].
                         Represents the diffusive momentum flux through each cell face.
        """
        vis_flux_face = grad_uvw_hat_face * \
                self.diffusion_coeff[:, None]
                
        return vis_flux_face
    
    def pressure_flux(self, p_new_face):
        """
        Compute pressure flux for momentum equations.
        
        Calculates the pressure flux using pressure values at face centers,
        weighted by pressure gradient coefficients. The pressure acts normal
        to each face surface.
        
        Args:
            p_face_new (torch.Tensor): Pressure values at face centers [N_faces, 1].
        
        Returns:
            torch.Tensor: Pressure flux tensor [N_cells_faces, 3, 3].
                         Diagonal tensor with pressure contribution to momentum flux.
        """
        P_flux_face = torch.diag_embed(p_new_face.expand(-1, 3)) * \
            self.grad_p_coeff[:, None]
        
        return P_flux_face
    
    def apply_pressure_outletBC(self, p_flux_face, grad_uvw_new_face):
        """
        Apply pressure outlet boundary condition.
        
        Modifies the pressure flux tensor to enforce the pressure outlet condition.
        The viscous force at the outlet should balance the pressure force.
        
        Args:
            p_flux_cells_face (torch.Tensor): Pressure flux tensor [N_cells_faces, 3, 3].
            grad_uvw_new_cells_face (torch.Tensor): Velocity gradients at face centers [N_faces, 3, 3].
        
        Returns:
            torch.Tensor: Modified pressure flux tensor [N_cells_faces, 3, 3].
        """

        if self.mask_outlet_face.any():
            # Compute viscous force at pressure outlet
            vis_grad = self.diffusion_coeff[:,None] * grad_uvw_new_face[:, 0:3]
            # Compute pressure force at outlet
            p_flux_face[self.mask_outlet_face, 0:3] = vis_grad[self.mask_outlet_face,0:3]
        
        return p_flux_face
                
    def continuity_flux(self, uvw_face):
        """
        Compute continuity equation residual using conserved form.
        
        Calculates the mass flux through cell faces to evaluate the continuity equation.
        Uses the divergence theorem to compute the net mass flux for each cell.
        
        Args:
            uvw_face_new (torch.Tensor): Velocity values at face centers [N_faces, 3].
                                       Contains u, v, w velocity components.
        
        Returns:
            torch.Tensor: Continuity equation residual [N_graphs, 1].
                         RMS of mass conservation violations for each graph in the batch.
        """
        # Compute mass flux through each face: velocity · surface_vector
        mass_flux = torch.matmul(
            uvw_face[self.cells_face, None, 0:3], 
            self.cells_face_surface_vec[:, :, None]
        ).squeeze().view(-1, 1)
        
        # # Sum fluxes for each cell using scatter operation
        # cell_divergence = scatter_add(
        #     mass_flux,
        #     self.cells_face_ptr,
        #     dim=0,
        #     dim_size=self.cells_volume.shape[0],
        # ).view(-1, 1)
        
        # # Compute RMS continuity residual for each graph in batch
        # loss_cont = torch.sqrt(
        #     global_add_pool(
        #         (cell_divergence)**2, batch=self.batch_cell
        #     )
        # ) * self.continuity_eq_coeff
        
        return mass_flux

            