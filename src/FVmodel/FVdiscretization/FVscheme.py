import sys
import os
from typing import Tuple, Optional, Any
file_dir = os.path.dirname(__file__)
sys.path.append(file_dir)

import torch
from torch_scatter import scatter_add, scatter_mean
from FVmodel.FVdiscretization.FVflux import FV_flux
from FVmodel.FVdiscretization.FVgrad import gradient_reconstruction
from torch_geometric.nn import global_add_pool

class Intergrator(FV_flux):
    """
    Finite Volume integrator class for assembling and solving discretized equations.

    Inherits from FV_flux to provide flux calculation capabilities.
    Handles both conserved and non-conserved forms of the Navier-Stokes equations.
    """

    def __init__(self, params: Any) -> None:
        """
        Initialize the finite volume integrator.

        Args:
            params: Configuration object containing discretization parameters,
                   time integration scheme, and equation form settings.
        """
        super(Intergrator, self).__init__()
        self.params = params

    def enforce_boundary_condition(self, uvwp_new_cpd_cell_non_cyclic: torch.Tensor, graph_cell: Any) -> torch.Tensor:
        """
        Enforce Dirichlet boundary conditions on the solution vector.

        Sets the velocity components at Dirichlet boundary cells to the specified
        boundary values from the graph data.

        Args:
            uvw_cell_new: Solution vector containing [u, v, w, p] values [N_cells, 4].
            graph_cell: Cell graph containing boundary condition values.

        Returns:
            Updated solution vector with boundary conditions applied.
        """

        uvw_cpd_cell_new = torch.zeros((self.num_cpd_cells, uvwp_new_cpd_cell_non_cyclic.shape[1]), device=uvwp_new_cpd_cell_non_cyclic.device)
        uvw_cpd_cell_new[self.mask_cpd_cell_non_cyclic] = uvwp_new_cpd_cell_non_cyclic
        uvw_cpd_cell_new[self.mask_dirichlet, 0:3] = graph_cell.y[:, 0:3]
        
        if self.mask_valid_cyclic_face.any():
            uvw_cpd_cell_new[self.patch1_cpd_cells] = uvw_cpd_cell_new[self.patch2_cells]
            uvw_cpd_cell_new[self.patch2_cpd_cells] = uvw_cpd_cell_new[self.patch1_cells]

        return uvw_cpd_cell_new
    
    def time_scheme(self, uvw_old_cell: torch.Tensor, uvw_new_cell: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Apply temporal discretization scheme for time integration.

        Computes the convective velocity field and unsteady term based on the
        selected time integration scheme (explicit, implicit, or IMEX).

        Args:
            uvw_cell_old: Previous time step velocity [N_cells, 3].
            uvw_cell_new: Current time step solution [N_cells, 4].

        Returns:
            Tuple containing:
                - unsteady_cell: Unsteady term contribution [N_interior_cells, 3].
                - uvw_hat_cell: Convective velocity field [N_cells, 3].
        """
        # Select convective velocity based on time integration scheme
        if self.params.integrator == "explicit":
            # Use previous time step velocity for convection
            uvw_hat_cell = uvw_old_cell[:, 0:3]
        elif self.params.integrator == "implicit":
            # Use current time step velocity for convection
            uvw_hat_cell = uvw_new_cell[:, 0:3]
        elif self.params.integrator == "imex":
            # Use average of previous and current for IMEX scheme
            uvw_hat_cell = (
                uvw_old_cell[:, 0:3] + uvw_new_cell[:, 0:3]
            ) / 2.0
        else:
            raise ValueError(f"Unknown integrator type: {self.params.integrator}")

        # Compute unsteady term: ∂u/∂t * V * coefficient
        # Note: Apply masks to ensure dimensional consistency
        unsteady_cell = (
            (uvw_new_cell[self.mask_interior_cell, 0:3] / self.dt_cell)
        ) * self.cells_volume * self.unsteady_coeff

        self.source_term = self.source_term + (
            (uvw_old_cell[self.mask_interior_cell, 0:3] / self.dt_cell)
        ) * self.cells_volume * self.unsteady_coeff

        return unsteady_cell, uvw_hat_cell
    
    def conserved_form(
        self,
        unsteady_cell: torch.Tensor,
        uvwp_new_hat_cpd_cell: Optional[torch.Tensor] = None,
        grad_uvwp_new_hat_cpd_cell: Optional[torch.Tensor] = None,
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Evaluate the conserved form of the governing equations.

        Computes the residuals for the continuity and momentum equations using
        the cell-centered and face-centered values. Applies boundary conditions
        and computes the total right-hand side for the linear system.

        Args:
            unsteady_cell: Unsteady term contribution [N_interior_cells, 3].
            uvwp_new_hat_cpd_cell: High-order interpolated values at cell centers [N_cpd_cells, 7].
            grad_uvwp_new_hat_cpd_cell: High-order gradients at cell centers [N_cpd_cells, 7, 3].

        Returns:
            Tuple containing:
                - loss_cont: Continuity equation residual for each graph in batch.
                - loss_momentum: Momentum equation residual for each graph in batch.
                - uvwp_new_cpd_cell: Updated and smoothed solution vector.
        """

        ''' >>> Interpolation >>> '''
        # Interpolate cell values to face centers using central difference + skewness correction
        uvwp_new_hat_face = self.interpolating_phic_to_faces(
            phi_cpd_cell=uvwp_new_hat_cpd_cell[:,0:7], # [N_cells, N_channels] where N_cells is number of cells, N_channels is number of variables
            grad_phi_cpd_cell=grad_uvwp_new_hat_cpd_cell[:,0:7], # [N_cells, N_channels, 3] where 3 represents gradient components in 3D
            scheme="Linear",
        )

        # Interpolate gradients to face centers
        grad_uvwp_new_hat_face = self.interpolating_gradients_to_faces(
            phi_cpd_cell=uvwp_new_hat_cpd_cell[:,0:7], # [N_cells, N_channels] where N_cells is number of cells, N_channels is number of variables
            grad_phi_cpd_cell=grad_uvwp_new_hat_cpd_cell[:,0:7], # [N_cells, N_channels, 3] where 3 represents gradient components in 3D
            presssure_location_dim=3,
        )
        ''' <<< Interpolation <<< '''

        ''' >>> Compute flux contributions >>> '''
        # Compute mass flux for continuity equation
        mass_flux_cells_face = self.continuity_flux(uvw_face=uvwp_new_hat_face[:,0:3])

        # Compute convective flux using specified scheme
        convection_flux_hat_face = self.convective_flux(
            uvw_face = uvwp_new_hat_face[:, 4:7], # for upwind mask
            phi_hat_cpd_cell = uvwp_new_hat_cpd_cell[:,0:3], 
            grad_phi_hat_cpd_cell = grad_uvwp_new_hat_cpd_cell[:,0:3], 
            grad_phi_hat_face = grad_uvwp_new_hat_face[:,0:3], 
            scheme="Linear"
        )

        # Compute pressure flux and apply boundary conditions
        P_flux_face = self.pressure_flux(p_new_face=uvwp_new_hat_face[:,3:4])
        P_flux_face = self.apply_pressure_outletBC(P_flux_face, grad_uvw_new_face = grad_uvwp_new_hat_face[:,0:3])

        # Compute viscous diffusion flux
        vis_flux_face = self.diffusion_flux(grad_uvw_hat_face=grad_uvwp_new_hat_face[:,0:3])
        ''' <<< Compute flux contributions <<< '''

        ''' >>> Total momentum flux assembly >>> '''
        J_flux_cells_face = torch.matmul(
            (convection_flux_hat_face + P_flux_face - vis_flux_face)[self.cells_face],
            self.cells_face_surface_vec.unsqueeze(-1)
        ).squeeze()

        # Assemble total right-hand side by scattering face fluxes to cells
        total_RHS = scatter_add(
            # torch.cat((mass_flux_cells_face, mass_flux_hat_cells_face, J_flux_cells_face), dim=1),
            torch.cat((mass_flux_cells_face, J_flux_cells_face), dim=1),
            self.cells_face_ptr,
            dim=0,
            dim_size=self.cells_volume.shape[0],
        )
        ''' <<< Total momentum flux assembly <<< '''

        ''' >>> Compute RMS residual for each graph in batch >>> '''
        # Compute RMS residual for continuity equation for each graph in batch
        loss_cont = torch.sqrt(
            global_add_pool(
                (total_RHS[:,0:1])**2, batch=self.batch_cell
            )
        ).sum(dim=1,keepdim=True) * self.continuity_eq_coeff
        
        # Compute RMS residual for momentum equations for each graph in batch
        loss_momentum = torch.sqrt(
            global_add_pool(
                (
                    unsteady_cell
                    + total_RHS[:,1:4]
                    - self.source_term
                )**2, batch=self.batch_cell
            )
        )
        ''' <<< Compute RMS residual for each graph in batch <<< '''

        ''' >>> Cell-Face-Cell (CFC) interpolation 2nd order >>> '''
        # # Apply second-order interpolation for solution smoothing
        # uvwp_new_cpd_cell_boudnary_smooth = uvwp_cell_new.clone()
        # uvwp_cell_new_boudnary_smooth = uvwp_new_cpd_cell_boudnary_smooth[self.mask_interior_cell]
        # uvwp_cell_new_smooth = self.interpolating_with_grad(
        #     src = uvw_cells_face_new[:,0:3],
        #     dst_index = self.cells_face_ptr,
        #     dst_size = self.num_cells,
        #     # src_grad = grad_uvw_cells_face_new[:, 0:3],
        #     # src_pos = self.face_pos[self.cells_face],
        #     # dst_pos = self.centroid[self.cells_face_ptr],
        # )
        # boudary_interior_cell = self.cells_face_ptr[self.mask_boundary_cells_face]
        # uvwp_cell_new_boudnary_smooth[boudary_interior_cell,0:3] = uvwp_cell_new_smooth[boudary_interior_cell,0:3] 
        # uvwp_new_cpd_cell_boudnary_smooth[self.mask_interior_cell] = uvwp_cell_new_boudnary_smooth
        # uvwp_cell_new_smooth = uvwp_cell_new
        uvwp_new_cpd_cell = uvwp_new_hat_cpd_cell[:,0:4]
        ''' <<< Cell-Face-Cell (CFC) interpolation 2nd order <<< '''
        
        return (
            loss_cont,
            loss_momentum,
            uvwp_new_cpd_cell,
        )

    def forward(
        self,
        uvw_old_cpd_cell: Optional[torch.Tensor] = None,
        uvwp_new_cpd_cell: Optional[torch.Tensor] = None,
        graph_node: Optional[Any] = None,
        graph_face: Optional[Any] = None,
        graph_cell: Optional[Any] = None,
        graph_cell_x: Optional[Any] = None,
        graph_Index: Optional[Any] = None,
        params: Optional[Any] = None,
    ) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Forward pass for the finite volume integrator.

        Performs gradient reconstruction, applies boundary conditions, computes temporal
        discretization, and evaluates the conserved form of the governing equations.

        Args:
            uvwp_cell_new: Current time step solution vector [N_cells, 4] containing [u, v, w, p].
            uvw_cell_old: Previous time step velocity [N_cells, 3] containing [u, v, w].
            graph_node: Node graph containing mesh node information.
            graph_face: Face graph containing mesh face information.
            graph_cell: Cell graph containing mesh cell information and boundary conditions.
            graph_cell_x: Extended cell graph containing WLSQ connectivity and matrices.
            graph_Index: Graph index containing global parameters and metadata.
            params: Configuration parameters containing discretization settings.

        Returns:
            Tuple containing:
                - loss_cont: Continuity equation residual for each graph in batch.
                - loss_momentum: Momentum equation residual for each graph in batch.
                - uvwp_cell_new: Updated and smoothed solution vector.
        """

        # Apply temporal discretization scheme at cell centers
        unsteady_cell, uvw_hat_cpd_cell = self.time_scheme(
            uvw_old_cell=uvw_old_cpd_cell, uvw_new_cell=uvwp_new_cpd_cell[:,0:3]
        )

        ''' >>> Gradient Reconstruction >>> '''
        # Combine current and intermediate solutions for gradient computation
        uvwp_new_hat_cpd_cell = torch.cat(
            (uvwp_new_cpd_cell[:, 0:4], uvw_hat_cpd_cell[:, 0:3]),
            dim=-1,
        )
        # Reconstruct gradients using weighted least squares method
        grad_uvwp_new_hat_cpd_cell = gradient_reconstruction(
            order=params.order,
            phi_node=uvwp_new_hat_cpd_cell,
            edge_index=graph_cell_x.cpd_neighbor_cell_x,
            mesh_pos=graph_cell.cpd_centroid,
            precompute_Moments=[graph_cell_x.A_cell_to_cell, graph_cell_x.single_B_cell_to_cell],
        )[:, :, 0:3]  # Extract gradients only: [N_cells, 7_variables, 3_spatial_dimensions]
        
        if self.mask_valid_cyclic_face.any():
            grad_uvwp_new_hat_cpd_cell[self.patch1_cpd_cells] = grad_uvwp_new_hat_cpd_cell[self.patch2_cells]
            grad_uvwp_new_hat_cpd_cell[self.patch2_cpd_cells] = grad_uvwp_new_hat_cpd_cell[self.patch1_cells]
        ''' <<< Gradient Reconstruction <<< '''

        # Apply conserved form of the finite volume method
        if params.conserved_form:
            (
                loss_cont,
                loss_momentum,
                uvwp_new_cpd_cell,
            ) = self.conserved_form(
                unsteady_cell=unsteady_cell,
                uvwp_new_hat_cpd_cell=uvwp_new_hat_cpd_cell,
                grad_uvwp_new_hat_cpd_cell=grad_uvwp_new_hat_cpd_cell,
            )
        else:
            raise NotImplementedError("Non-conserved form has been removed")

        return (
            loss_cont,
            loss_momentum,
            uvwp_new_cpd_cell,
        )
