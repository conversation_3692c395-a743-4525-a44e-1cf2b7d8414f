import sys
import os
import logging
import math
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple, List

file_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.append(file_dir)

import numpy as np
import torch
import h5py
from torch_scatter import scatter
from Post_process.to_vtk import write_hybrid_mesh_to_vtu
from Utils.utilities import NodeType

# 将输出缓冲区设置为0
sys.stdout.flush()


def string_to_floats(s):
    """将字符串转换为一组浮点数"""
    return np.asarray([float(ord(c)) for c in s])


def floats_to_string(floats):
    """将一组浮点数转换为字符串"""
    return "".join([chr(int(f)) for f in floats])


class BaseMeshManager(ABC):
    """
    Abstract base class for mesh managers.

    Provides common functionality for mesh processing, validation, and visualization.
    Subclasses should implement specific mesh format parsing logic.
    """

    def __init__(self):
        """Initialize base mesh manager"""
        # Common mesh attributes that subclasses should populate
        self.mesh_pos = None
        self.node_type = None
        self.faces_pos = None
        self.centroid = None
        self.face_node = None
        self.face_node_ptr = None
        self.cells_node = None
        self.cells_node_ptr = None
        self.cells_face = None
        self.cells_face_ptr = None
        self.neighbor_cell = None
        self.faces_type = None
        self.faces_areas = None
        self.faces_normals = None
        self.cells_volume = None

        # Compound attributes
        self.cpd_cell_type = None
        self.cpd_neighbor_cell = None
        self.cpd_neighbor_cell_x = None
        self.cpd_neighbor_cell_non_cyclic = None
        self.cpd_centroid = None

        # PyVista format data
        self.pv_cells_node = None
        self.pv_cells_type = None

    @abstractmethod
    def _extract_base_mesh_data(self):
        """Extract basic mesh data from source format"""
        pass

    @abstractmethod
    def _calculate_connectivity_data(self):
        """Calculate mesh connectivity data"""
        pass

    def far_field_boundary_split(self, dataset):
        """Legacy method - to be implemented by subclasses if needed"""
        pass

    def _process_boundary_data_unified(self, boundary_raw: Dict, boundary_conditions_0: Optional[Dict] = None, boundary_conditions_polymesh: Optional[Dict] = None) -> Dict:
        """
        Unified boundary processing method that extracts all boundary information in a single pass.

        Args:
            boundary_raw: Raw boundary data from mesh format
            boundary_conditions_0: boundary conditions from 0 field files
            boundary_conditions_polymesh: detailed boundary information from polymesh file

        Returns:
            Dictionary containing processed boundary information
        """
        boundary_info = {
            'boundary_faces': set(),
            'cyclic_patches': {},
            'face_types': {},
            'patch_info': {}
        }

        for boundary_name, boundary_props in boundary_raw.items():
            # Handle both dictionary and namedtuple formats
            if isinstance(boundary_props, dict):
                start_face = boundary_props.get('startFace', 0)
                n_faces = boundary_props.get('nFaces', 0)
                geom_type = boundary_props.get('type', 'unknown')
                neighbour_patch = boundary_props.get('neighbourPatch', None)
            elif hasattr(boundary_props, 'start') and hasattr(boundary_props, 'num'):
                start_face = boundary_props.start
                n_faces = boundary_props.num
                geom_type = boundary_props.type if hasattr(boundary_props, 'type') else "unknown"
                neighbour_patch = getattr(boundary_props, 'neighbourPatch', None)
            else:
                logging.warning("Unknown boundary data format for %s: %s", boundary_name, boundary_props)
                continue

            # Handle byte strings
            if isinstance(geom_type, bytes):
                geom_type = geom_type.decode('utf-8')
            if isinstance(boundary_name, bytes):
                boundary_name = boundary_name.decode('utf-8')

            # Get additional info from detailed boundary info if available
            if boundary_conditions_polymesh and boundary_name in boundary_conditions_polymesh:
                detailed_info = boundary_conditions_polymesh[boundary_name]
                neighbour_patch = detailed_info.get('neighbourPatch', neighbour_patch)

            # Store patch information
            patch_info = {
                'start_face': start_face,
                'n_faces': n_faces,
                'geom_type': geom_type,
                'neighbour_patch': neighbour_patch
            }
            boundary_info['patch_info'][boundary_name] = patch_info

            # Add boundary faces to set
            face_range = range(start_face, start_face + n_faces)
            boundary_info['boundary_faces'].update(face_range)

            # Determine face types and check for cyclic boundaries
            if boundary_conditions_0:
                u_bc_type = 'unknown'
                p_bc_type = 'unknown'

                if 'U' in boundary_conditions_0 and boundary_name in boundary_conditions_0['U']:
                    u_bc_type = boundary_conditions_0['U'][boundary_name]['type']

                if 'p' in boundary_conditions_0 and boundary_name in boundary_conditions_0['p']:
                    p_bc_type = boundary_conditions_0['p'][boundary_name]['type']

                if 't' in boundary_conditions_0 and boundary_name in boundary_conditions_0['t']:
                    p_bc_type = boundary_conditions_0['t'][boundary_name]['type']
                # Check for cyclic boundaries from boundary conditions
                if u_bc_type == 'cyclic' or p_bc_type == 'cyclic' or geom_type == 'cyclic':
                    boundary_info['cyclic_patches'][boundary_name] = patch_info

                logging.info(f"🔍 Calling mapping for {boundary_name}: u={u_bc_type}, p={p_bc_type}, geom={geom_type}")
                node_type = self._map_boundary_conditions_to_nodetype(boundary_name, u_bc_type, p_bc_type, geom_type)
                logging.info(f"🎯 Mapped {boundary_name} to {NodeType(node_type).name}")
            else:
                # Process cyclic boundaries based on geometry type only
                if geom_type == 'cyclic':
                    boundary_info['cyclic_patches'][boundary_name] = patch_info

                node_type = NodeType.WALL_BOUNDARY  # Default for unknown boundaries

            # Store face types for this patch
            for face_idx in face_range:
                boundary_info['face_types'][face_idx] = node_type

        return boundary_info

    def _map_boundary_conditions_to_nodetype(self, boundary_name: str, u_bc_type: str, p_bc_type: str, geom_type: str) -> NodeType:
        """
        Map OpenFOAM boundary conditions to NodeType enum values.

        Args:
            boundary_name: Name of the boundary patch
            u_bc_type: Velocity boundary condition type
            p_bc_type: Pressure boundary condition type
            geom_type: Geometric type of the boundary

        Returns:
            Corresponding NodeType enum value
        """
        # Handle cyclic boundaries first (check both geometry type and boundary conditions)
        logging.info(f"🔍 Checking cyclic conditions for {boundary_name}: geom='{geom_type}' (type: {type(geom_type)}), u='{u_bc_type}' (type: {type(u_bc_type)}), p='{p_bc_type}' (type: {type(p_bc_type)})")
        logging.info(f"🔍 Condition checks: geom=='cyclic': {geom_type == 'cyclic'}, u=='cyclic': {u_bc_type == 'cyclic'}, p=='cyclic': {p_bc_type == 'cyclic'}")

        if geom_type == 'cyclic' or u_bc_type == 'cyclic' or p_bc_type == 'cyclic':
            logging.info(f"✅ Mapping {boundary_name} to CYCLIC: geom={geom_type}, u={u_bc_type}, p={p_bc_type}")
            return NodeType.CYCLIC  # Cyclic boundaries have their own type

        # Map based on velocity boundary conditions (primary)
        if u_bc_type in ['fixedValue', 'uniformFixedValue', 'flowRateInletVelocity']:
            return NodeType.INFLOW
        elif u_bc_type in ['zeroGradient', 'pressureInletOutletVelocity']:
            return NodeType.OUTFLOW
        elif u_bc_type in ['noSlip', 'slip', 'fixedFluxPressure']:
            return NodeType.WALL_BOUNDARY

        # Map based on pressure boundary conditions (secondary)
        elif p_bc_type in ['fixedValue', 'uniformFixedValue']:
            return NodeType.OUTFLOW
        elif p_bc_type in ['zeroGradient']:
            return NodeType.WALL_BOUNDARY

        # # Default mapping based on boundary name patterns
        # boundary_lower = boundary_name.lower()
        # if any(keyword in boundary_lower for keyword in ['inlet', 'inflow', 'input']):
        #     return NodeType.INFLOW
        # elif any(keyword in boundary_lower for keyword in ['outlet', 'outflow', 'output', 'exit']):
        #     return NodeType.OUTFLOW
        # elif any(keyword in boundary_lower for keyword in ['wall', 'boundary', 'solid']):
        #     return NodeType.WALL_BOUNDARY

        # Default to wall boundary for unknown types
        logging.error("Unknown boundary condition for %s (U:%s, p:%s, geom:%s), defaulting to WALL_BOUNDARY",
                       boundary_name, u_bc_type, p_bc_type, geom_type)
        
        raise ValueError(f"Unknown boundary condition for {boundary_name} (U:{u_bc_type}, p:{p_bc_type}, geom:{geom_type})")

    def _get_boundary_type_name(self, node_type_value: int) -> str:
        """Get human-readable name for boundary type"""
        try:
            return NodeType(node_type_value).name
        except ValueError:
            return f"UNKNOWN_{node_type_value}"

    def _calculate_face_geometric_properties_vectorized(self, faces_raw: List, mesh_pos: torch.Tensor, faces_pos: torch.Tensor, face_node: torch.Tensor=None, face_node_ptr: torch.Tensor=None,) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Calculate face normals and areas using truly vectorized operations.
        Groups faces by number of vertices and processes each group in parallel.

        Args:
            faces_raw: List of face vertex indices
            mesh_pos: Node positions [N, 3]
            faces_pos: Face center positions [F, 3]

        Returns:
            Tuple of (face_normals [F, 3], face_areas [F])
        """
        from Parse_mesh.parse_to_h5 import seperate_domain

        num_faces = len(faces_raw)
        faces_normals = torch.zeros(num_faces, 3, dtype=torch.float64)
        faces_areas = torch.zeros(num_faces, dtype=torch.float64)

        # Use seperate_domain to group faces by number of vertices
        domain_list = seperate_domain(
            cells_ptr=face_node_ptr,
            cells_node=face_node,
            cells_face=None
        )

        # Process each group of faces with same number of vertices
        for n_vertices, local_face_node, _, local_faces_ptr, _ in domain_list:
            if local_face_node is None or len(local_face_node) == 0:
                continue

            # Calculate number of faces in this group
            num_faces_in_group = len(local_face_node) // n_vertices

            # Verify the calculation is correct
            if len(local_face_node) != num_faces_in_group * n_vertices:
                logging.warning(f"Face group size mismatch: {len(local_face_node)} nodes for {num_faces_in_group} faces with {n_vertices} vertices each")
                continue

            # Reshape to [num_faces_in_group, n_vertices]
            local_face_node_reshaped = local_face_node.reshape(num_faces_in_group, n_vertices)
            local_faces_ptr_reshaped = local_faces_ptr.reshape(num_faces_in_group, n_vertices)[:,0]

            # Get vertex positions: [num_faces_in_group, n_vertices, 3]
            vertices_positions = mesh_pos[local_face_node_reshaped]

            # Get face centers for this group: [num_faces_in_group, 3]
            group_face_centers = torch.mean(vertices_positions,dim=1)

            # Calculate normals and areas for this group using vectorized operations
            group_normals, group_areas = self._calculate_polygon_properties_vectorized(
                vertices_positions, group_face_centers, n_vertices
            )

            # Store results back to the main arrays
            faces_normals[local_faces_ptr_reshaped] = group_normals
            faces_areas[local_faces_ptr_reshaped] = group_areas

        return faces_normals, faces_areas

    def _calculate_polygon_properties_vectorized(self, vertices_positions: torch.Tensor,
                                               face_centers: torch.Tensor,
                                               n_vertices: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Calculate normals and areas for a batch of polygons with the same number of vertices.
        Uses fan triangulation from face center to compute properties.

        Args:
            vertices_positions: [num_faces, n_vertices, 3] - vertex positions for each face
            face_centers: [num_faces, 3] - face center positions
            n_vertices: number of vertices per face

        Returns:
            Tuple of (normals [num_faces, 3], areas [num_faces])
        """
        num_faces = vertices_positions.shape[0]

        # Create vectors from face center to each vertex: [num_faces, n_vertices, 3]
        center_to_vertices = vertices_positions - face_centers.unsqueeze(1)

        # Get vectors to next vertex (cyclic): [num_faces, n_vertices, 3]
        # For each vertex i, get vector to vertex (i+1) % n_vertices
        next_vertices = torch.roll(center_to_vertices, -1, dims=1)

        # Calculate cross products for each triangle formed by center and adjacent vertices
        # cross_products[i, j] = cross(center_to_vertex[i,j], center_to_vertex[i,j+1])
        cross_products = torch.cross(center_to_vertices, next_vertices, dim=2)  # [num_faces, n_vertices, 3]

        # Sum all cross products to get face normal (before normalization)
        # This works because the sum of cross products of a fan triangulation gives the face normal
        face_normal_unnormalized = torch.sum(cross_products, dim=1)  # [num_faces, 3]

        # Calculate face areas as sum of triangle areas
        # Area of each triangle = 0.5 * |cross_product|
        triangle_areas = 0.5 * torch.norm(cross_products, dim=2)  # [num_faces, n_vertices]
        face_areas = torch.sum(triangle_areas, dim=1)  # [num_faces]

        # Normalize face normals to unit vectors
        face_normal_magnitudes = torch.norm(face_normal_unnormalized, dim=1, keepdim=True)  # [num_faces, 1]

        face_normals = face_normal_unnormalized/face_normal_magnitudes

        return face_normals, face_areas

    def _validate_mesh_consistency(self):
        """Validate mesh data consistency"""
        logging.info("Validating mesh consistency...")

        # Check that all required attributes are present
        required_attrs = ['mesh_pos', 'faces_pos', 'centroid', 'face_node', 'cells_node']
        for attr in required_attrs:
            if getattr(self, attr, None) is None:
                raise ValueError(f"Required mesh attribute '{attr}' is not set")

        # Check tensor shapes and data types
        if self.mesh_pos.dim() != 2 or self.mesh_pos.shape[1] != 3:
            raise ValueError(f"mesh_pos should be [N, 3], got {self.mesh_pos.shape}")

        if self.faces_pos.dim() != 2 or self.faces_pos.shape[1] != 3:
            raise ValueError(f"faces_pos should be [F, 3], got {self.faces_pos.shape}")

        if self.centroid.dim() != 2 or self.centroid.shape[1] != 3:
            raise ValueError(f"centroid should be [C, 3], got {self.centroid.shape}")

        logging.info("  ✓ Mesh consistency validation passed")

    def save_to_hdf5(self, output_path: str, case_name: str, **metadata):
        """
        Save mesh data to HDF5 format with structured naming convention.

        Args:
            output_path: Path where the HDF5 file will be saved
            case_name: Name of the case for metadata
            **metadata: Additional metadata to store
        """
        with h5py.File(output_path, 'w') as hf:
            # Add metadata
            hf.attrs['case_name'] = case_name
            hf.attrs['dimension'] = '3D'

            # Add custom metadata
            for key, value in metadata.items():
                hf.attrs[key] = value

            # Geometry data
            if self.mesh_pos is not None:
                hf.create_dataset('node|pos', data=self.mesh_pos.to(torch.float32).numpy())
            if self.node_type is not None:
                hf.create_dataset('node|node_type', data=self.node_type.numpy())
            if self.faces_pos is not None:
                hf.create_dataset('face|face_pos', data=self.faces_pos.to(torch.float32).numpy())
            if self.centroid is not None:
                hf.create_dataset('cell|centroid', data=self.centroid.to(torch.float32).numpy())

            # Connectivity data
            if self.face_node is not None:
                hf.create_dataset('face_node', data=self.face_node.numpy())
            if self.face_node_ptr is not None:
                hf.create_dataset('face_node_ptr', data=self.face_node_ptr.numpy())
            if self.cells_node is not None:
                hf.create_dataset('cells_node', data=self.cells_node.numpy())
            if self.cells_node_ptr is not None:
                hf.create_dataset('cells_node_ptr', data=self.cells_node_ptr.numpy())
            if self.cells_face is not None:
                hf.create_dataset('cells_face', data=self.cells_face.numpy())
            if self.cells_face_ptr is not None:
                hf.create_dataset('cells_face_ptr', data=self.cells_face_ptr.numpy())

            # Face and cell properties
            if self.faces_type is not None:
                hf.create_dataset('face|face_type', data=self.faces_type.numpy())
            if self.faces_areas is not None:
                hf.create_dataset('face|face_area', data=self.faces_areas.to(torch.float32).numpy())
            if self.cells_volume is not None:
                hf.create_dataset('cell|cells_volume', data=self.cells_volume.to(torch.float32).numpy())

            # Compound attributes
            if self.cpd_cell_type is not None:
                hf.create_dataset('cpd|cell_type', data=self.cpd_cell_type.numpy())
            if self.cpd_neighbor_cell is not None:
                hf.create_dataset('cpd|neighbor_cell', data=self.cpd_neighbor_cell.numpy())
            if self.cpd_neighbor_cell_x is not None:
                hf.create_dataset('cpd|neighbor_cell_x', data=self.cpd_neighbor_cell_x.numpy())
            if self.cpd_neighbor_cell_non_cyclic is not None:
                hf.create_dataset('cpd|neighbor_cell_non_cyclic', data=self.cpd_neighbor_cell_non_cyclic.numpy())
            if self.cpd_centroid is not None:
                hf.create_dataset('cpd|centroid', data=self.cpd_centroid.to(torch.float32).numpy())

            # PyVista format data
            if self.pv_cells_node is not None:
                hf.create_dataset('pv_cells_node', data=self.pv_cells_node.numpy())
            if self.pv_cells_type is not None:
                hf.create_dataset('pv_cells_type', data=self.pv_cells_type.numpy())

        logging.info("Successfully saved mesh to HDF5: %s", output_path)

    def create_visualizations(self, output_dir: str, case_name: str, enable_matplotlib: bool = False, enable_pyvista: bool = True, **kwargs):
        """
        Create visualizations using available visualization backends.

        Args:
            output_dir: Directory to save visualization files
            case_name: Name of the case for file naming
            enable_matplotlib: Whether to create matplotlib visualization
            enable_pyvista: Whether to create PyVista visualization
            **kwargs: Additional arguments passed to visualization methods
        """
        try:
            from .Utils.visualization_base import MatplotlibVisualization, PyVistaVisualization

            vis_output_dir = os.path.join(output_dir, 'Graph_visualization')
            os.makedirs(vis_output_dir, exist_ok=True)

            if enable_matplotlib:
                matplotlib_vis = MatplotlibVisualization(self)
                matplotlib_vis.create_visualization(vis_output_dir, case_name, **kwargs)

            if enable_pyvista:
                pyvista_vis = PyVistaVisualization(self)
                pyvista_vis.create_visualization(vis_output_dir, case_name, **kwargs)

        except ImportError as e:
            logging.error("Visualization dependencies not available: %s", e)
        except Exception as e:
            logging.error("Visualization creation failed: %s", e)



    def _get_boundary_type_name(self, node_type_value: int) -> str:
        """Get human-readable name for boundary type"""
        try:
            return NodeType(node_type_value).name
        except ValueError:
            return f"UNKNOWN_{node_type_value}"

    def triangles_to_faces(self, faces, mesh_pos, deform=False):
        """Computes mesh edges from triangles."""
        mesh_pos = torch.from_numpy(mesh_pos)
        if not deform:
            # collect edges from triangles
            edges = torch.cat(
                (
                    faces[:, 0:2],
                    faces[:, 1:3],
                    torch.stack((faces[:, 2], faces[:, 0]), dim=1),
                ),
                dim=0,
            )
            # those edges are sometimes duplicated (within the mesh) and sometimes
            # single (at the mesh boundary).
            # sort & pack edges as single tf.int64
            receivers, _ = torch.min(edges, dim=1)
            senders, _ = torch.max(edges, dim=1)

            packed_edges = torch.stack((senders, receivers), dim=1)
            unique_edges = torch.unique(
                packed_edges, return_inverse=False, return_counts=False, dim=0
            )
            senders, receivers = torch.unbind(unique_edges, dim=1)
            senders = senders.to(torch.int64)
            receivers = receivers.to(torch.int64)

            two_way_connectivity = (
                torch.cat((senders, receivers), dim=0),
                torch.cat((receivers, senders), dim=0),
            )
            unique_edges = torch.stack((senders, receivers), dim=1)

            # plot_edge_direction(mesh_pos,unique_edges)

            # face_with_bias = reorder_face(mesh_pos,unique_edges,plot=True)
            # edge_with_bias = reorder_face(mesh_pos,packed_edges,plot=True)

            return {
                "two_way_connectivity": two_way_connectivity,
                "senders": senders,
                "receivers": receivers,
                "unique_edges": unique_edges,
                "face_with_bias": unique_edges,
                "edge_with_bias": packed_edges,
            }

        else:
            edges = torch.cat(
                (
                    faces[:, 0:2],
                    faces[:, 1:3],
                    faces[:, 2:4],
                    torch.stack((faces[:, 3], faces[:, 0]), dim=1),
                ),
                dim=0,
            )
            # those edges are sometimes duplicated (within the mesh) and sometimes
            # single (at the mesh boundary).
            # sort & pack edges as single tf.int64
            receivers, _ = torch.min(edges, dim=1)
            senders, _ = torch.max(edges, dim=1)

            packed_edges = torch.stack((senders, receivers), dim=1)
            unique_edges = torch.unique(
                packed_edges, return_inverse=False, return_counts=False, dim=0
            )
            senders, receivers = torch.unbind(unique_edges, dim=1)
            senders = senders.to(torch.int64)
            receivers = receivers.to(torch.int64)

            two_way_connectivity = (
                torch.cat((senders, receivers), dim=0),
                torch.cat((receivers, senders), dim=0),
            )
            return {
                "two_way_connectivity": two_way_connectivity,
                "senders": senders,
                "receivers": receivers,
            }

    def position_relative_to_line_pytorch(A, B, angle_c):
        # A是点的坐标，表示为(x, y)的元组
        # B是一个数组，shape为[nums, 2]，其中nums为参与判断的点数量，2为xy坐标
        # angle_c是与X轴的夹角，以角度为单位

        # 将输入转换为张量
        A = torch.tensor(A, dtype=torch.float64)
        B = torch.tensor(B, dtype=torch.float64)
        angle_c = torch.tensor(angle_c, dtype=torch.float64)

        # 计算直线的方向向量
        direction_vector = torch.tensor(
            [
                torch.cos(angle_c * math.pi / 180.0),
                torch.sin(angle_c * math.pi / 180.0),
            ],
            dtype=torch.float64,
        )

        # 计算向量AB
        vector_AB = B - A

        # 计算两个向量的叉积，注意这里使用广播
        cross_product = (
            direction_vector[0] * vector_AB[:, 1]
            - direction_vector[1] * vector_AB[:, 0]
        )

        # 判断每个点相对于直线的位置，返回一个mask
        mask = cross_product > 0
        return mask.view(-1, 1)  # 调整shape为[nums, 1]

    def is_convex(self, polygon):
        """
        检查一个多边形是否是凸的。
        :param polygon: 多边形的顶点坐标，一个二维numpy数组。
        :return: 如果是凸的返回True，否则返回False。
        """
        n = len(polygon)
        for i in range(n):
            a = polygon[i]
            b = polygon[(i + 1) % n]
            c = polygon[(i + 2) % n]
            ba = a - b
            bc = c - b
            cross_product = np.cross(ba, bc)
            if cross_product < 0:
                return False
        return True

    def reorder_polygon(self, polygon):
        """
        重新排序多边形的顶点使其成为一个凸多边形。
        :param polygon: 多边形的顶点坐标，一个二维numpy数组。
        :return: 重新排序后的多边形的顶点坐标。
        """
        centroid = np.mean(polygon, axis=0)
        sorted_polygon = sorted(
            polygon, key=lambda p: np.arctan2(p[1] - centroid[1], p[0] - centroid[0])
        )
        return np.array(sorted_polygon)

    def ensure_counterclockwise(self, cells, mesh_pos):
        """
        确保每个单元的顶点是按逆时针顺序排列的，并且是凸的。
        :param cells: 单元的索引数组。
        :param mesh_pos: 顶点的坐标数组。
        :return: 调整后的cells数组。
        """
        for i, cell in enumerate(cells):
            vertices = mesh_pos[cell]
            if not self.is_convex(vertices):
                vertices = self.reorder_polygon(vertices)
                sorted_indices = sorted(
                    range(len(cell)),
                    key=lambda k: list(map(list, vertices)).index(
                        list(mesh_pos[cell][k])
                    ),
                )
                cells[i] = np.array(cell)[sorted_indices]
        return cell

    def is_equal(self, x, pivot):
        """
        Determine if a value x is between two other values a and b.

        Parameters:
        - a (float or int): The lower bound.
        - b (float or int): The upper bound.
        - x (float or int): The value to check.

        Returns:
        - (bool): True if x is between a and b (inclusive), False otherwise.
        """
        a = abs(pivot) - float(1e-8)
        b = abs(pivot) + float(1e-8)
        # Check if x is between a and b, inclusive
        if a <= abs(x) <= b:
            return True
        else:
            return False
        
    def convert_to_tensors(self, input_dict: dict) -> dict:
        """Convert dictionary numeric values to PyTorch tensors.
        
        Args:
            input_dict: Dictionary with numpy arrays or numeric values
            
        Returns:
            Dictionary with numeric values converted to PyTorch tensors
        """
        output_dict = {}
        
        try:
            for key, value in input_dict.items():
                # Handle nested dictionaries
                if isinstance(value, dict):
                    output_dict[key] = self.convert_to_tensors(value)
                # Convert numpy arrays    
                elif isinstance(value, np.ndarray):
                    output_dict[key] = torch.from_numpy(value.copy())
                # Convert numeric types
                elif isinstance(value, (int, float, bool, list, tuple)):
                    output_dict[key] = torch.tensor(value)
                # Keep tensors as-is
                elif isinstance(value, torch.Tensor):
                    output_dict[key] = value.clone()
                # Keep strings and other types unchanged
                else:
                    output_dict[key] = value
                    
        except Exception as e:
            raise ValueError(f"Failed to convert to tensor: {str(e)}")

        return output_dict
    
    def convert_to_numpy(self, input_dict: dict) -> dict:
        """Convert dictionary values from PyTorch tensors/numeric types to numpy arrays.
        
        Args:
            input_dict: Dictionary containing PyTorch tensors and other values
            
        Returns:
            Dictionary with tensors and numeric values converted to numpy arrays
        """
        output_dict = {}
        
        try:
            for key, value in input_dict.items():
                # Handle nested dictionaries
                if isinstance(value, dict):
                    output_dict[key] = self.convert_to_numpy(value)
                # Convert PyTorch tensors
                elif isinstance(value, torch.Tensor):
                    output_dict[key] = value.detach().cpu().numpy()
                # Keep numpy arrays as-is    
                elif isinstance(value, np.ndarray):
                    output_dict[key] = value.copy()
                # Convert numeric types to numpy arrays
                elif isinstance(value, (int, float, bool, list, tuple)):
                    output_dict[key] = np.array(value)
                # Keep strings and other types unchanged
                else:
                    output_dict[key] = value
                    
        except Exception as e:
            raise ValueError(f"Failed to convert to numpy: {str(e)}")

        return output_dict
    
    def save_to_vtu(self, mesh:dict, payload:dict, file_name):
        """
        使用 PyVista 写入包含多个顶点和单元数据的 vtu 文件。

        参数:
        - mesh: 字典，包含网格数据，
        - payload: 字典，包含顶点或单元数据，键名以 'node|' 开头表示顶点数据，以 'cell|' 开头表示单元数据
                例如: {'node|temperature': [...], 'cell|pressure': [...]}
        - cells_node: 单元信息，格式为 [顶点数, 顶点1, 顶点2, ...]，例如 [4, 0,1,2,3, 3, 4,5,6]
        - filename: 保存的文件名，默认为 'output.vtu'
        """
        
        # first to tensor
        mesh = self.convert_to_tensors(mesh)
        
        # 暂时先写vtu来可视化
        mesh_pos = mesh["node|pos"].squeeze() if "node|pos" in mesh else mesh["mesh_pos"].squeeze()
        cells_node = mesh["cells_node"].long().squeeze()
        cells_index = mesh["cells_index"].long().squeeze()
   
        write_hybrid_mesh_to_vtu(
            mesh_pos=mesh_pos.cpu().numpy(), 
            data=payload, 
            cells_node=cells_node.cpu(), 
            cells_ptr=cells_index.cpu(),
            file_path=file_name
        )
